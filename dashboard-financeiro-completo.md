# Dashboard Financeiro Completo - Academia SaaS

## 📊 Visão Geral

Este documento especifica todos os dados, gráficos e análises que devem estar presentes no dashboard financeiro completo da academia. O dashboard deve fornecer uma visão abrangente e em tempo real do estado financeiro da academia.

## 🎯 Objetivos do Dashboard

- **Visão 360°**: Panorama completo da saúde financeira
- **Tomada de Decisão**: Dados para decisões estratégicas
- **Monitoramento**: Acompanhamento de KPIs financeiros
- **Previsibilidade**: Projeções e tendências
- **Controle**: Gestão de receitas e despesas

## 📈 Métricas Principais (KPIs)

### 1. Receitas
- **Receita Total do Mês**: Soma de todos os pagamentos confirmados
- **Receita Recorrente Mensal (MRR)**: Receita previsível mensal
- **Receita Média por Aluno (ARPU)**: Receita total / número de alunos ativos
- **Taxa de Crescimento da Receita**: Comparação mês a mês
- **Receita Projetada**: Baseada em planos ativos e histórico

### 2. Pagamentos
- **Pagamentos Confirmados**: Quantidade e valor
- **Pagamentos Pendentes**: Aguardando confirmação
- **Pagamentos em Atraso**: Vencidos e não pagos
- **Taxa de Inadimplência**: % de pagamentos em atraso
- **Tempo Médio de Recebimento**: Dias entre vencimento e pagamento

### 3. Despesas
- **Despesas Totais do Mês**: Soma de todas as despesas pagas
- **Despesas por Categoria**: Breakdown por tipo de gasto
- **Despesas Pendentes**: Aguardando pagamento
- **Despesas Vencidas**: Em atraso
- **Margem de Lucro**: (Receita - Despesas) / Receita * 100

### 4. Fluxo de Caixa
- **Saldo Atual**: Receitas - Despesas
- **Projeção de Fluxo**: Próximos 3-6 meses
- **Entradas Previstas**: Pagamentos agendados
- **Saídas Previstas**: Despesas programadas

### 5. Alunos e Retenção
- **Alunos Ativos**: Com mensalidades em dia
- **Taxa de Retenção**: % de alunos que permanecem
- **Churn Rate**: % de alunos que cancelam
- **Lifetime Value (LTV)**: Valor médio por aluno ao longo do tempo
- **Customer Acquisition Cost (CAC)**: Custo para adquirir novo aluno

## 📊 Gráficos e Visualizações

### 1. Gráficos de Receita
- **Receita Mensal (Linha)**: Evolução ao longo do ano
- **Receita vs Meta (Barras)**: Comparação com objetivos
- **Receita por Modalidade (Pizza)**: Distribuição por tipo de aula
- **Receita por Forma de Pagamento (Barras)**: PIX, cartão, dinheiro, etc.

### 2. Gráficos de Pagamentos
- **Status de Pagamentos (Pizza)**: Pago, pendente, atrasado
- **Evolução de Inadimplência (Linha)**: Taxa ao longo do tempo
- **Pagamentos por Dia do Mês (Heatmap)**: Padrões de pagamento
- **Métodos de Pagamento Preferidos (Barras)**: Uso por método

### 3. Gráficos de Despesas
- **Despesas por Categoria (Pizza)**: Aluguel, equipamentos, salários, etc.
- **Evolução de Despesas (Linha)**: Tendência mensal
- **Despesas Fixas vs Variáveis (Barras empilhadas)**
- **Comparação Orçado vs Realizado (Barras)**

### 4. Gráficos de Fluxo de Caixa
- **Fluxo de Caixa Mensal (Barras)**: Entradas e saídas
- **Projeção de Fluxo (Linha)**: Próximos meses
- **Saldo Acumulado (Área)**: Evolução do saldo
- **Sazonalidade (Linha)**: Padrões anuais

### 5. Gráficos de Crescimento
- **Crescimento de Receita (Linha)**: MoM e YoY
- **Crescimento de Alunos (Linha)**: Evolução da base
- **Funil de Conversão (Funil)**: Lead → Matrícula → Retenção
- **Cohort Analysis (Heatmap)**: Retenção por período de entrada

## 🗂️ Dados das Tabelas do Banco

### Tabelas Principais
1. **payments**: Receitas e pagamentos dos alunos
2. **expenses**: Despesas da academia
3. **memberships**: Matrículas e planos ativos
4. **students**: Base de alunos
5. **plans**: Planos e preços
6. **expense_categories**: Categorias de despesas
7. **payment_methods**: Formas de pagamento

### Campos Importantes
- **payments**: amount, status, due_date, paid_at, payment_method
- **expenses**: amount, status, due_date, paid_at, category_id
- **memberships**: status, start_date, end_date, next_billing_date
- **students**: state (active/inactive)
- **plans**: pricing_config, duration_config

## 🎨 Layout e Organização

### Seção 1: Resumo Executivo (Topo)
- Cards com KPIs principais
- Indicadores de tendência (↑↓)
- Comparação com período anterior
- Alertas e notificações importantes

### Seção 2: Receitas (Esquerda)
- Gráfico de receita mensal
- Breakdown por modalidade
- Status de pagamentos
- Projeções

### Seção 3: Despesas (Centro)
- Gráfico de despesas por categoria
- Despesas pendentes
- Comparação orçado vs realizado
- Margem de lucro

### Seção 4: Fluxo de Caixa (Direita)
- Gráfico de fluxo mensal
- Projeção próximos meses
- Saldo atual
- Entradas/saídas previstas

### Seção 5: Análises Avançadas (Inferior)
- Crescimento e tendências
- Análise de cohort
- Métricas de retenção
- Comparações históricas

## 🔧 Funcionalidades Interativas

### Filtros
- **Período**: Último mês, trimestre, semestre, ano
- **Modalidade**: Filtrar por tipo de aula
- **Forma de Pagamento**: Filtrar por método
- **Status**: Filtrar por status de pagamento/despesa
- **Categoria**: Filtrar despesas por categoria

### Drill-down
- Clicar em gráficos para ver detalhes
- Navegação para listas detalhadas
- Links para perfis de alunos
- Acesso a relatórios específicos

### Exportação
- Relatórios em PDF
- Dados em Excel/CSV
- Gráficos em imagem
- Relatórios personalizados

## 📱 Responsividade

### Desktop
- Layout em grid 3-4 colunas
- Gráficos grandes e detalhados
- Múltiplas métricas visíveis

### Tablet
- Layout em 2 colunas
- Gráficos médios
- Navegação por abas

### Mobile
- Layout em 1 coluna
- Cards empilhados
- Gráficos simplificados
- Swipe entre seções

## 🚀 Implementação Técnica

### Componentes Necessários
1. **DashboardFinanceiro**: Componente principal
2. **MetricasResumo**: Cards de KPIs
3. **GraficoReceitas**: Visualizações de receita
4. **GraficoDespesas**: Visualizações de despesas
5. **FluxoCaixa**: Análise de fluxo
6. **TabelaTransacoes**: Lista detalhada
7. **FiltrosPeriodo**: Controles de filtro
8. **ExportacaoRelatorios**: Funcionalidades de export

### Bibliotecas de Gráficos
- **Recharts**: Para gráficos principais
- **Chart.js**: Para gráficos avançados
- **D3.js**: Para visualizações customizadas

### Atualização de Dados
- **Real-time**: WebSockets para dados críticos
- **Polling**: Atualização periódica (5-10 min)
- **Manual**: Botão de refresh
- **Cache**: Otimização de performance

## 📊 Métricas de Performance

### Indicadores de Sucesso
- Tempo de carregamento < 3s
- Taxa de uso do dashboard > 80%
- Satisfação dos usuários > 4.5/5
- Redução de tempo para insights > 50%

### Monitoramento
- Analytics de uso
- Feedback dos usuários
- Performance de queries
- Tempo de resposta da API

## 🔍 Análises Específicas Necessárias

### 1. Análise de Receita
```sql
-- Receita mensal por status
SELECT
  DATE_TRUNC('month', due_date) as mes,
  status,
  COUNT(*) as quantidade,
  SUM(amount) as valor_total
FROM payments
WHERE due_date >= DATE_TRUNC('year', CURRENT_DATE)
GROUP BY mes, status
ORDER BY mes DESC;

-- Receita por forma de pagamento
SELECT
  payment_method,
  COUNT(*) as transacoes,
  SUM(amount) as valor_total,
  AVG(amount) as ticket_medio
FROM payments
WHERE status = 'paid'
  AND paid_at >= DATE_TRUNC('month', CURRENT_DATE)
GROUP BY payment_method;
```

### 2. Análise de Despesas
```sql
-- Despesas por categoria
SELECT
  ec.name as categoria,
  ec.color,
  COUNT(e.*) as quantidade,
  SUM(e.amount) as valor_total,
  AVG(e.amount) as valor_medio
FROM expenses e
JOIN expense_categories ec ON e.category_id = ec.id
WHERE e.created_at >= DATE_TRUNC('month', CURRENT_DATE)
GROUP BY ec.id, ec.name, ec.color;

-- Evolução de despesas mensais
SELECT
  DATE_TRUNC('month', due_date) as mes,
  SUM(amount) as total_despesas,
  COUNT(*) as quantidade_despesas
FROM expenses
WHERE due_date >= DATE_TRUNC('year', CURRENT_DATE)
GROUP BY mes
ORDER BY mes;
```

### 3. Análise de Fluxo de Caixa
```sql
-- Fluxo de caixa mensal
WITH receitas AS (
  SELECT
    DATE_TRUNC('month', paid_at) as mes,
    SUM(amount) as receita
  FROM payments
  WHERE status = 'paid' AND paid_at IS NOT NULL
  GROUP BY mes
),
despesas AS (
  SELECT
    DATE_TRUNC('month', paid_at) as mes,
    SUM(amount) as despesa
  FROM expenses
  WHERE status = 'paid' AND paid_at IS NOT NULL
  GROUP BY mes
)
SELECT
  COALESCE(r.mes, d.mes) as mes,
  COALESCE(r.receita, 0) as receita,
  COALESCE(d.despesa, 0) as despesa,
  COALESCE(r.receita, 0) - COALESCE(d.despesa, 0) as saldo
FROM receitas r
FULL OUTER JOIN despesas d ON r.mes = d.mes
ORDER BY mes;
```

### 4. Análise de Alunos e Retenção
```sql
-- Alunos ativos por mês
SELECT
  DATE_TRUNC('month', start_date) as mes_entrada,
  COUNT(*) as novos_alunos,
  COUNT(CASE WHEN status = 'active' THEN 1 END) as ainda_ativos
FROM memberships
GROUP BY mes_entrada
ORDER BY mes_entrada;

-- Taxa de inadimplência
SELECT
  DATE_TRUNC('month', due_date) as mes,
  COUNT(*) as total_pagamentos,
  COUNT(CASE WHEN status = 'overdue' THEN 1 END) as atrasados,
  ROUND(
    COUNT(CASE WHEN status = 'overdue' THEN 1 END) * 100.0 / COUNT(*), 2
  ) as taxa_inadimplencia
FROM payments
WHERE due_date >= DATE_TRUNC('year', CURRENT_DATE)
GROUP BY mes
ORDER BY mes;
```

## 🎯 Componentes Específicos a Implementar

### 1. FinancialDashboard (Componente Principal)
```typescript
interface FinancialDashboardProps {
  initialPeriod?: DateRange;
  refreshInterval?: number;
}

interface DashboardData {
  kpis: FinancialKPIs;
  revenueData: RevenueAnalysis;
  expenseData: ExpenseAnalysis;
  cashFlowData: CashFlowAnalysis;
  studentMetrics: StudentMetrics;
  charts: ChartData[];
}
```

### 2. KPICards (Métricas Principais)
```typescript
interface FinancialKPIs {
  totalRevenue: {
    current: number;
    previous: number;
    growth: number;
  };
  monthlyRecurringRevenue: {
    current: number;
    previous: number;
    growth: number;
  };
  averageRevenuePerUser: {
    current: number;
    previous: number;
    growth: number;
  };
  churnRate: {
    current: number;
    previous: number;
    change: number;
  };
  profitMargin: {
    current: number;
    previous: number;
    change: number;
  };
}
```

### 3. RevenueCharts (Gráficos de Receita)
```typescript
interface RevenueAnalysis {
  monthlyRevenue: MonthlyData[];
  revenueByModality: CategoryData[];
  revenueByPaymentMethod: CategoryData[];
  paymentStatus: StatusData[];
  projections: ProjectionData[];
}
```

### 4. ExpenseCharts (Gráficos de Despesas)
```typescript
interface ExpenseAnalysis {
  monthlyExpenses: MonthlyData[];
  expensesByCategory: CategoryData[];
  fixedVsVariable: ComparisonData[];
  budgetVsActual: ComparisonData[];
  pendingExpenses: PendingData[];
}
```

### 5. CashFlowAnalysis (Análise de Fluxo)
```typescript
interface CashFlowAnalysis {
  monthlyCashFlow: CashFlowData[];
  projectedCashFlow: ProjectionData[];
  currentBalance: number;
  expectedInflows: number;
  expectedOutflows: number;
  seasonalityPattern: SeasonalData[];
}
```

## 📋 Checklist de Implementação

### Fase 1: Estrutura Base
- [x] Criar componente FinancialDashboard principal
- [x] Implementar layout responsivo
- [x] Configurar sistema de filtros
- [x] Implementar loading states

### Fase 2: Métricas e KPIs
- [x] Implementar cards de KPIs principais
- [x] Adicionar comparações com período anterior
- [x] Implementar indicadores de tendência
- [ ] Adicionar alertas e notificações (ainda não implementado sistema de notificação na plataforma)

### Fase 3: Gráficos de Receita
- [x] Gráfico de receita mensal
- [x] Gráfico de receita por modalidade
- [x] Gráfico de status de pagamentos
- [x] Gráfico de métodos de pagamento

### Fase 4: Gráficos de Despesas
- [x] Gráfico de despesas por categoria
- [x] Gráfico de evolução de despesas
- [x] Gráfico de despesas fixas vs variáveis
- [x] Lista de despesas pendentes

### Fase 5: Análise de Fluxo de Caixa
- [ ] Gráfico de fluxo mensal
- [ ] Projeções de fluxo
- [ ] Indicadores de saldo
- [ ] Análise de sazonalidade

### Fase 6: Otimização e Performance
- [ ] Implementar cache de dados
- [ ] Otimizar queries do banco
- [ ] Implementar lazy loading
- [ ] Adicionar analytics de uso

## 🔗 Integrações Necessárias

### APIs e Serviços
- **Supabase**: Dados principais do banco
- **Recharts**: Biblioteca de gráficos
- **Date-fns**: Manipulação de datas
- **React Query**: Cache e sincronização
- **Zustand**: Gerenciamento de estado

### Componentes Reutilizáveis
- Cards de métricas existentes
- Componentes de gráfico da biblioteca
- Filtros de data existentes
- Componentes de loading
- Sistema de notificações

## 📈 Roadmap de Evolução

### Versão 1.0 (MVP)
- KPIs básicos
- Gráficos principais
- Filtros simples
- Layout responsivo

### Versão 1.1
- Drill-down em gráficos
- Exportação básica
- Comparações históricas
- Alertas automáticos

### Versão 1.2
- Análises avançadas
- Projeções inteligentes
- Relatórios customizados
- Integração com BI

### Versão 2.0
- Machine Learning para previsões
- Dashboards personalizáveis
- API para terceiros
- Mobile app dedicado
